{"id": "willemstad-curaao", "name": "Willemstad, Curaçao", "country": "Curaçao", "cables": [{"id": "alonso-de-ojeda", "name": "<PERSON>", "rfs_year": 1999, "is_planned": false}, {"id": "americas-ii", "name": "Americas-II", "rfs_year": 2000, "is_planned": false}, {"id": "amerigo-<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "rfs_year": 1999, "is_planned": false}, {"id": "arcos", "name": "ARCOS", "rfs_year": 2001, "is_planned": false}, {"id": "eclink", "name": "ECLink", "rfs_year": 2007, "is_planned": false}, {"id": "jerry-newton", "name": "<PERSON>", "rfs_year": 2007, "is_planned": false}], "landing_points": ["nassau-bahamas", "crooked-island-bahamas", "providenciales-turks-and-caicos-islands", "punta-cana-dominican-republic", "puerto-plata-dominican-republic", "riohacha-colombia", "bluefields-nicaragua", "puerto-lempira-honduras", "trujillo-honduras", "puerto-cortes-honduras", "puerto-barrios-guatemala", "puerto-cabezas-nicaragua", "fortaleza-brazil", "cayenne-french-guiana", "le-lamentin-martinique", "miramar-pr-united-states", "port-of-spain-trinidad-and-tobago", "hollywood-fl-united-states", "st-croix-virgin-islands-u-s-", "camuri-venezuela", "cartagena-colombia", "cat-island-bahamas", "punto-fijo-venezuela", "north-miami-beach-fl-united-states", "tulum-mexico", "isla-verde-pr-united-states", "belize-city-belize", "puerto-limon-costa-rica", "cancn-mexico", "chaguaramas-trinidad-and-tobago", "maria-chiquita-panama", "willemstad-curaao", "north-salina-bonaire-sint-eustatius-and-saba", "kralendijk-bonaire-netherlands", "baby-beach-aruba"]}