{"id": "canada", "name": "Canada", "cables": [{"id": "cogim", "name": "COGIM", "rfs_year": 2005, "is_planned": false}, {"id": "crosslake-fibre", "name": "Crosslake Fibre", "rfs_year": 2019, "is_planned": false}, {"id": "eastern-arctic-undersea-fibre-optic-network-eaufon", "name": "Eastern Arctic Undersea Fibre Optic Network (EAUFON)", "rfs_year": 2022, "is_planned": true}, {"id": "exa-express", "name": "EXA Express", "rfs_year": 2015, "is_planned": false}, {"id": "exa-north-and-south", "name": "EXA North and South", "rfs_year": 2001, "is_planned": false}, {"id": "greenland-connect", "name": "Greenland Connect", "rfs_year": 2009, "is_planned": false}, {"id": "hdr1", "name": "HDR1", "rfs_year": null, "is_planned": false}, {"id": "hdr2", "name": "HDR2", "rfs_year": null, "is_planned": false}, {"id": "kattittuq-nunavut-fibre-link", "name": "Kattittuq Nunavut Fibre Link", "rfs_year": 2025, "is_planned": true}, {"id": "ketchcan1-submarine-fiber-cable-system", "name": "KetchCan1 Submarine Fiber Cable System", "rfs_year": 2020, "is_planned": false}, {"id": "le<PERSON>-<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "rfs_year": 2024, "is_planned": true}, {"id": "persona", "name": "<PERSON>a", "rfs_year": 2008, "is_planned": false}, {"id": "sednalink-fibre", "name": "SednaLink Fibre", "rfs_year": 2023, "is_planned": true}, {"id": "st-pierre-and-miquelon-cable", "name": "St. Pierre and Miquelon Cable", "rfs_year": 2018, "is_planned": false}, {"id": "topaz", "name": "Topaz", "rfs_year": 2023, "is_planned": true}], "landing_points": ["halifax-ns-canada", "lynn-ma-united-states", "dublin-ireland", "southport-united-kingdom", "nuuk-greenland", "milton-nl-canada", "qaqortoq-greenland", "landeyjar-iceland", "coleraine-united-kingdom", "rose-blanche-nl-canada", "new-victoria-ns-canada", "brean-united-kingdom", "cork-ireland", "toronto-on-canada", "buffalo-ny-united-states", "lamaline-nl-canada", "fortune-nl-canada", "miquelon-langlade-saint-pierre-and-miquelon", "saint-pierre-saint-pierre-and-miquelon", "ketchikan-ak-united-states", "prince-rupert-bc-canada", "chisasibi-qc-canada", "puvirnituq-qc-canada", "umiujaq-qc-canada", "kuujjuarapik-qc-canada", "inukjuak-qc-canada", "lanse--beaufils-qc-canada", "les-les-de-la-madeleine-qc-canada", "port-alberni-bc-canada", "iqaluit-nu-canada", "goose-bay-nl-canada", "akulivik-qc-canada", "ivujivik-qc-canada", "kangiqsujuaq-qc-canada", "salluit-qc-canada", "stavanger-norway", "vancouver-bc-canada", "shima-japan", "takahagi-japan", "dingwall-ns-canada", "codroy-nl-canada", "saint-john-nb-canada", "aylesford-ns-canada", "sydney-mines-ns-canada", "cape-ray-nl-canada"], "landing_points_in_country": ["toronto-on-canada", "vancouver-bc-canada", "halifax-ns-canada", "prince-rupert-bc-canada", "port-alberni-bc-canada", "milton-nl-canada", "saint-john-nb-canada", "new-victoria-ns-canada", "rose-blanche-nl-canada", "iqaluit-nu-canada", "chisasibi-qc-canada", "inukjuak-qc-canada", "salluit-qc-canada", "kuujjuarapik-qc-canada", "umiujaq-qc-canada", "puvirnituq-qc-canada", "akulivik-qc-canada", "ivujivik-qc-canada", "kangiqsujuaq-qc-canada", "goose-bay-nl-canada", "lamaline-nl-canada", "fortune-nl-canada", "lanse--beaufils-qc-canada", "les-les-de-la-madeleine-qc-canada", "dingwall-ns-canada", "codroy-nl-canada", "aylesford-ns-canada", "sydney-mines-ns-canada", "cape-ray-nl-canada"]}