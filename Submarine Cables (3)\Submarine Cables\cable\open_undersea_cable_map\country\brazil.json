{"id": "brazil", "name": "Brazil", "cables": [{"id": "america-movil-submarine-cable-system-1-amx-1", "name": "America Movil Submarine Cable System-1 (AMX-1)", "rfs_year": 2014, "is_planned": false}, {"id": "americas-ii", "name": "Americas-II", "rfs_year": 2000, "is_planned": false}, {"id": "brazilian-festoon", "name": "Brazilian Festoon", "rfs_year": 1996, "is_planned": false}, {"id": "brusa", "name": "BRUSA", "rfs_year": 2018, "is_planned": false}, {"id": "ellalink", "name": "EllaLink", "rfs_year": 2021, "is_planned": false}, {"id": "firmina", "name": "Firmina", "rfs_year": 2023, "is_planned": true}, {"id": "globenet", "name": "GlobeNet", "rfs_year": 2000, "is_planned": false}, {"id": "junior", "name": "Junior", "rfs_year": 2018, "is_planned": false}, {"id": "malbec", "name": "Malbec", "rfs_year": 2021, "is_planned": false}, {"id": "monet", "name": "Monet", "rfs_year": 2017, "is_planned": false}, {"id": "seabras-1", "name": "Seabras-1", "rfs_year": 2017, "is_planned": false}, {"id": "south-america-1-sam-1", "name": "South America-1 (SAm-1)", "rfs_year": 2001, "is_planned": false}, {"id": "south-american-crossing-sac", "name": "South American Crossing (SAC)", "rfs_year": 2000, "is_planned": false}, {"id": "south-atlantic-cable-system-sacs", "name": "South Atlantic Cable System (SACS)", "rfs_year": 2018, "is_planned": false}, {"id": "south-atlantic-inter-link-sail", "name": "South Atlantic Inter Link (SAIL)", "rfs_year": 2020, "is_planned": false}, {"id": "tannat", "name": "<PERSON><PERSON>", "rfs_year": 2018, "is_planned": false}], "landing_points": ["st-croix-virgin-islands-u-s-", "valparaso-chile", "fortaleza-brazil", "lurin-peru", "rio-de-janeiro-brazil", "santos-brazil", "las-toninas-argentina", "fort-amador-panama", "tuckerton-nj-united-states", "st-davids-bermuda", "boca-raton-fl-united-states", "san-juan-pr-united-states", "arica-chile", "puerto-san-jose-guatemala", "puerto-barrios-guatemala", "cayenne-french-guiana", "le-lamentin-martinique", "miramar-pr-united-states", "port-of-spain-trinidad-and-tobago", "hollywood-fl-united-states", "camuri-venezuela", "salvador-brazil", "puerto-viejo-venezuela", "barranquilla-colombia", "punta-carnero-ecuador", "mancora-peru", "jacksonville-fl-united-states", "cancn-mexico", "cartagena-colombia", "puerto-plata-dominican-republic", "sangano-angola", "willemstad-curaao", "buenaventura-colombia", "kribi-cameroon", "maldonado-uruguay", "sines-portugal", "wall-township-nj-united-states", "praia-grande-brazil", "virginia-beach-va-united-states", "so-mateus-brazil", "porto-seguro-brazil", "ilhus-brazil", "aracaju-brazil", "macei-brazil", "recife-brazil", "joo-pessoa-brazil", "natal-brazil", "vitria-brazil", "maca-brazil", "sitio-brazil", "atafona-brazil", "praia-cape-verde", "funchal-portugal", "punta-cana-dominican-republic", "maiquetia-venezuela", "colon-panama", "punta-del-este-uruguay", "schooner-bight-colombia", "myrtle-beach-sc-united-states"], "landing_points_in_country": ["rio-de-janeiro-brazil", "salvador-brazil", "vitria-brazil", "macei-brazil", "fortaleza-brazil", "joo-pessoa-brazil", "recife-brazil", "natal-brazil", "aracaju-brazil", "maca-brazil", "santos-brazil", "ilhus-brazil", "porto-seguro-brazil", "so-mateus-brazil", "praia-grande-brazil", "sitio-brazil", "atafona-brazil"]}