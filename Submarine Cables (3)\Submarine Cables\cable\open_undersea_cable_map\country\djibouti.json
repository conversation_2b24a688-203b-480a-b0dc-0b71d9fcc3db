{"id": "djibouti", "name": "Djibouti", "cables": [{"id": "2africa", "name": "2Africa", "rfs_year": 2023, "is_planned": true}, {"id": "aden-djibouti", "name": "Aden-Djibouti", "rfs_year": 1994, "is_planned": false}, {"id": "africa-1", "name": "Africa-1", "rfs_year": 2023, "is_planned": true}, {"id": "asia-africa-europe-1-aae-1", "name": "Asia Africa Europe-1 (AAE-1)", "rfs_year": 2017, "is_planned": false}, {"id": "djibouti-africa-regional-express-1-dare1", "name": "Djibouti Africa Regional Express 1 (DARE1)", "rfs_year": 2021, "is_planned": false}, {"id": "eastern-africa-submarine-system-eassy", "name": "Eastern Africa Submarine System (EASSy)", "rfs_year": 2010, "is_planned": false}, {"id": "europe-india-gateway-eig", "name": "Europe India Gateway (EIG)", "rfs_year": 2011, "is_planned": false}, {"id": "india-europe-xpress-iex", "name": "India Europe Xpress (IEX)", "rfs_year": 2024, "is_planned": true}, {"id": "middle-east-north-africa-mena-cable-systemgulf-bridge-international", "name": "Middle East North Africa (MENA) Cable System/Gulf Bridge International", "rfs_year": 2014, "is_planned": false}, {"id": "peace-cable", "name": "PEACE Cable", "rfs_year": 2022, "is_planned": false}, {"id": "<PERSON>an", "name": "<PERSON><PERSON>", "rfs_year": 2024, "is_planned": true}, {"id": "seacomtata-tgn-eurasia", "name": "SEACOM/Tata TGN-Eurasia", "rfs_year": 2009, "is_planned": false}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "seamewe-5", "name": "SeaMeWe-5", "rfs_year": 2016, "is_planned": false}, {"id": "seamewe-6", "name": "SeaMeWe-6", "rfs_year": 2025, "is_planned": true}], "landing_points": ["deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "chania-greece", "ttouan-morocco", "ostend-belgium", "haramous-djibouti", "mombasa-kenya", "dar-es-salaam-tanzania", "maputo-mozambique", "toliara-madagascar", "mtunzini-south-africa", "aden-yemen", "port-sudan-sudan", "zafarana-egypt", "abu-talat-egypt", "barka-oman", "monaco-monaco", "tripoli-libya", "bude-united-kingdom", "gibraltar-gibraltar", "al-seeb-oman", "moroni-comoros", "marseille-france", "doha-qatar", "songkhla-thailand", "sihanoukville-cambodia", "al-bustan-oman", "toulon-france", "catania-italy", "yanbu-saudi-arabia", "qalhat-oman", "ngwe-saung-myanmar", "melaka-malaysia", "kuakata-bangladesh", "al-hudaydah-yemen", "matara-sri-lanka", "bari-italy", "mogadishu-somalia", "dumai-indonesia", "cape-daguilar-china", "vung-tau-vietnam", "bosaso-somalia", "gwadar-pakistan", "berbera-somalia", "kalba-united-arab-emirates", "victoria-seychelles", "kismayo-somalia", "hobyo-somalia", "geoje-south-korea", "ras-ghareb-egypt", "port-said-egypt", "genoa-italy", "barcelona-spain", "salal<PERSON>-<PERSON>man", "nacala-mozambique", "mahajanga-madagascar", "cape-town-south-africa", "port-elizabeth-south-africa", "muanda-congo-dem-rep-", "pointe-noire-congo-rep-", "libreville-gabon", "lagos-nigeria", "accra-ghana", "abidjan-cte-divoire", "dakar-senegal", "carcavelos-portugal", "duba-saudi-arabia", "mocha-yemen", "sidi-kerir-egypt", "savona-italy", "shantou-china", "mellieha-malta", "aqaba-jordan", "kwa-ibo-nigeria", "luanda-angola", "gran-canaria-canary-islands-spain", "carana-seychelles", "butterworth-malaysia", "timpaki-greece", "abu-dhabi-united-arab-emirates", "manama-bahrain", "al-faw-iraq", "kuwait-city-kuwait", "al-khobar-saudi-arabia", "hulhumale-maldives", "coxs-bazar-bangladesh", "morib-malaysia", "kulhudhufushi-maldives", "tympaki-greece"], "landing_points_in_country": ["djibouti-city-djibouti", "haramous-djibouti"]}