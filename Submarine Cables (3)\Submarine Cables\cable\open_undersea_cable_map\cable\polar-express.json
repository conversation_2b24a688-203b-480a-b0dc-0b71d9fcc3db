{"id": "polar-express", "name": "Polar Express", "length": "12,650 km", "landing_points": [{"id": "anadyr-russia", "name": "Anadyr, Russia", "country": "Russia", "is_tbd": null}, {"id": "dikson-russia", "name": "Di<PERSON>on, Russia", "country": "Russia", "is_tbd": null}, {"id": "murmansk-russia", "name": "Murmansk, Russia", "country": "Russia", "is_tbd": null}, {"id": "nakhodka-russia", "name": "Nakhodka, Russia", "country": "Russia", "is_tbd": null}, {"id": "okrug-russia", "name": "Okrug, Russia", "country": "Russia", "is_tbd": null}, {"id": "petropavlovsk-kamchatsky-russia", "name": "Petropavlovsk-Kamchatsky, Russia", "country": "Russia", "is_tbd": null}, {"id": "pevek-russia", "name": "Pevek, Russia", "country": "Russia", "is_tbd": null}, {"id": "sakha-republic-russia", "name": "Sakha Republic, Russia", "country": "Russia", "is_tbd": null}, {"id": "vladivostok-russia", "name": "Vladivostok, Russia", "country": "Russia", "is_tbd": null}, {"id": "yuzhno-sakhalinsk-russia", "name": "Yuzhno-Sakhalinsk, Russia", "country": "Russia", "is_tbd": null}], "owners": "Russian Government", "suppliers": null, "rfs": "2026", "rfs_year": 2026, "is_planned": true, "url": null, "notes": null}