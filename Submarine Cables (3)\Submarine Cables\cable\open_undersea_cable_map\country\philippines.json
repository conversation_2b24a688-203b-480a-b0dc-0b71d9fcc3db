{"id": "philippines", "name": "Philippines", "cables": [{"id": "apcn-2", "name": "APCN-2", "rfs_year": 2001, "is_planned": false}, {"id": "apricot", "name": "Apricot", "rfs_year": 2024, "is_planned": true}, {"id": "asia-america-gateway-aag-cable-system", "name": "Asia-America Gateway (AAG) Cable System", "rfs_year": 2009, "is_planned": false}, {"id": "asia-direct-cable-adc", "name": "Asia Direct Cable (ADC)", "rfs_year": 2023, "is_planned": true}, {"id": "asia-submarine-cable-express-asecahaya-malaysia", "name": "Asia Submarine-cable Express (ASE)/Cahaya Malaysia", "rfs_year": 2012, "is_planned": false}, {"id": "bifrost", "name": "Bifrost", "rfs_year": 2024, "is_planned": true}, {"id": "boracay-palawan-submarine-cable-system-bpscs", "name": "Boracay-Palawan Submarine Cable System (BPSCS)", "rfs_year": 2013, "is_planned": false}, {"id": "cap-1", "name": "CAP-1", "rfs_year": 2023, "is_planned": true}, {"id": "converge-domestic-submarine-cable-network-cdscn", "name": "Converge Domestic Submarine Cable Network (CDSCN)", "rfs_year": 2021, "is_planned": false}, {"id": "eac-c2c", "name": "EAC-C2C", "rfs_year": 2002, "is_planned": false}, {"id": "jupiter", "name": "JUPITER", "rfs_year": 2020, "is_planned": false}, {"id": "national-digital-transmission-network-ndtn", "name": "National Digital Transmission Network (NDTN)", "rfs_year": 1999, "is_planned": false}, {"id": "pacific-light-cable-network-plcn", "name": "Pacific Light Cable Network (PLCN)", "rfs_year": 2022, "is_planned": false}, {"id": "palawan-iloilo-cable-system", "name": "Palawan-Iloilo Cable System", "rfs_year": 2014, "is_planned": false}, {"id": "philippine-domestic-submarine-cable-network-pdscn", "name": "Philippine Domestic Submarine Cable Network (PDSCN)", "rfs_year": 2023, "is_planned": true}, {"id": "pldt-domestic-fiber-optic-network-dfon", "name": "PLDT Domestic Fiber Optic Network (DFON)", "rfs_year": 1997, "is_planned": false}, {"id": "sea-h2x", "name": "SEA-H2X", "rfs_year": 2024, "is_planned": true}, {"id": "sea-us", "name": "SEA-US", "rfs_year": 2017, "is_planned": false}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "sorsogon-samar-submarine-fiber-optical-interconnection-project-sssfoip", "name": "Sorsogon-Samar Submarine Fiber Optical Interconnection Project (SSSFOIP)", "rfs_year": 2019, "is_planned": false}, {"id": "southeast-asia-japan-cable-sjc", "name": "Southeast Asia-Japan Cable (SJC)", "rfs_year": 2013, "is_planned": false}, {"id": "tata-tgn-intra-asia-tgn-ia", "name": "Tata TGN-Intra Asia (TGN-IA)", "rfs_year": 2009, "is_planned": false}], "landing_points": ["deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "chikura-japan", "busan-south-korea", "shantou-china", "kuantan-malaysia", "kitaibaraki-japan", "lantau-island-china", "chongming-china", "katong-singapore", "tanshui-taiwan", "chania-greece", "ttouan-morocco", "ostend-belgium", "lucena-philippines", "san-jose-philippines", "dumaguete-philippines", "sriracha-thailand", "vung-tau-vietnam", "san-luis-obispo-ca-united-states", "ballesteros-philippines", "keawaula-hi-united-states", "tanguisson-point-guam", "ajigaura-japan", "shima-japan", "cavite-philippines", "pa-li-taiwan", "shindu-ri-south-korea", "tseung-kwan-o-china", "qingdao-china", "chung-hom-kok-china", "changi-south-singapore", "nasugbu-philippines", "telisai-brunei", "la-union-philippines", "daet-philippines", "maruyama-japan", "coron-philippines", "taytay-philippines", "changi-north-singapore", "san-jose-de-buenavista-philippines", "piti-guam", "davao-philippines", "hermosa-beach-ca-united-states", "pinamalayan-philippines", "roxas-city-philippines", "masbate-city-philippines", "legazpi-city-philippines", "calbayog-philippines", "ormoc-philippines", "cebu-philippines", "cadiz-city-philippines", "cagayan-de-oro-philippines", "butuan-city-philippines", "makaha-hi-united-states", "kauditan-indonesia", "ozamiz-city-philippines", "el-segundo-ca-united-states", "magachgil-yap-micronesia", "ngeremlengui-palau", "santa-magdalena-philippines", "allen-philippines", "iloilo-city-philippines", "cloverdale-or-united-states", "komesu-japan", "pasacao-philippines", "bogo-philippines", "naga-philippines", "buenavista-philippines", "toledo-philippines", "san-carlos-philippines", "san-remigio-philippines", "talisay-city-philippines", "leganes-philippines", "milagros-philippines", "roxas-philippines", "boracay-philippines", "san-juan-philippines", "bacong-philippines", "tagbilaran-philippines", "baclayon-philippines", "geoje-south-korea", "baler-philippines", "quy-nhon-vietnam", "grover-beach-ca-united-states", "pagudpud-philippines", "nanhui-china", "singapore-singapore", "jakarta-indonesia", "minamiboso-japan", "kuching-malaysia", "songkhla-thailand", "lingshui-china", "caticlan-philippines", "tanjung-pakis-indonesia", "batam-indonesia", "agat-guam", "ilijan-philippines", "boac-philippines", "calatrava-philippines", "placer-philippines", "bacolod-philippines", "zamboanguita-philippines", "dipolog-city-philippines", "surigao-city-philippines", "maasin-philippines", "liloan-philippines", "palompon-philippines", "palanas-philippines", "bulan-philippines", "cagdianao-philippines", "siargao-island-philippines", "kinoguitan-philippines", "camiguin-island-philippines", "talisay-philippines", "liloy-philippines", "zamboanga-city-philippines", "rosarito-mexico", "balikpapan-indonesia", "manado-indonesia", "winema-road-beach-or-united-states"], "landing_points_in_country": ["batangas-philippines", "cebu-philippines", "davao-philippines", "cavite-philippines", "lucena-philippines", "roxas-city-philippines", "san-jose-philippines", "dumaguete-philippines", "ballesteros-philippines", "bacolod-philippines", "bogo-philippines", "calbayog-philippines", "coron-philippines", "ormoc-philippines", "zamboanga-city-philippines", "nasugbu-philippines", "la-union-philippines", "daet-philippines", "cagayan-de-oro-philippines", "iloilo-city-philippines", "taytay-philippines", "san-jose-de-buenavista-philippines", "pinamalayan-philippines", "masbate-city-philippines", "legazpi-city-philippines", "cadiz-city-philippines", "ozamiz-city-philippines", "butuan-city-philippines", "naga-philippines", "pasacao-philippines", "santa-magdalena-philippines", "allen-philippines", "buenavista-philippines", "toledo-philippines", "san-carlos-philippines", "san-remigio-philippines", "talisay-city-philippines", "leganes-philippines", "milagros-philippines", "roxas-philippines", "boracay-philippines", "san-juan-philippines", "bacong-philippines", "tagbilaran-philippines", "baclayon-philippines", "baler-philippines", "pagudpud-philippines", "caticlan-philippines", "ilijan-philippines", "boac-philippines", "calatrava-philippines", "placer-philippines", "zamboanguita-philippines", "dipolog-city-philippines", "surigao-city-philippines", "maasin-philippines", "liloan-philippines", "palompon-philippines", "palanas-philippines", "bulan-philippines", "cagdianao-philippines", "siargao-island-philippines", "kinoguitan-philippines", "camiguin-island-philippines", "liloy-philippines", "talisay-philippines"]}